# Environment Configuration
NODE_ENV=development

# Application
NEXT_PUBLIC_APP_NAME=ENERCY
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Backend API
BACKEND_PORT=3001
BACKEND_URL=http://localhost:3001

# Database
MONGODB_URI=mongodb://localhost:27017/enercy_db
MONGODB_TEST_URI=mongodb://localhost:27017/enercy_test_db

# Blockchain Configuration
NEXT_PUBLIC_CHAIN_ID=11155111
NEXT_PUBLIC_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
NEXT_PUBLIC_ENRC_CONTRACT_ADDRESS=0x...

# Private Keys (NEVER COMMIT THESE)
PRIVATE_KEY=your_wallet_private_key_here
INFURA_PROJECT_ID=your_infura_project_id
ALCHEMY_API_KEY=your_alchemy_api_key

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your_refresh_token_secret_here

# Security
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn_here
GOOGLE_ANALYTICS_ID=your_ga_id_here

# External APIs
COINMARKETCAP_API_KEY=your_cmc_api_key
ENERGY_DATA_API_KEY=your_energy_api_key

# Development
DEBUG=true
LOG_LEVEL=debug

# Production Security Headers
NEXT_PUBLIC_CSP_NONCE=random_nonce_here
