import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Mock data for demonstration
const mockUserData = {
    usina: {
        name: "Usina Solar Alfa",
        balance: 1500000,
        generation: 50000,
        type: "usina"
    },
    distribuidora: {
        name: "Distribuidora Beta",
        balance: 800000,
        consumption: 30000,
        type: "distribuidora"
    },
    consumidor: {
        name: "Consumidor Gama",
        balance: 5000,
        consumption: 500,
        type: "consumidor"
    }
};

const Dashboard = ({ userType }) => {
    const [balance, setBalance] = useState(0);
    const [connectedAccount, setConnectedAccount] = useState(null);
    const [userData, setUserData] = useState(mockUserData[userType]);

    useEffect(() => {
        // Simulate fetching data from backend/blockchain
        const fetchData = async () => {
            // In a real application, you would connect to MetaMask and fetch actual token balance
            // and user-specific data from your backend API.
            if (window.ethereum) {
                try {
                    const provider = new ethers.providers.Web3Provider(window.ethereum);
                    const accounts = await provider.listAccounts();
                    if (accounts.length > 0) {
                        setConnectedAccount(accounts[0]);
                        // Replace with your actual token contract address and ABI
                        const tokenAddress = "SEU_ENDERECO_DO_CONTRATO_ENRC"; // <<<<<<< ATUALIZE AQUI
                        const tokenAbi = [
                            "function balanceOf(address account) view returns (uint256)"
                        ];
                        const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, provider);
                        const rawBalance = await tokenContract.balanceOf(accounts[0]);
                        setBalance(ethers.utils.formatUnits(rawBalance, 18)); // Assuming 18 decimals
                    }
                } catch (error) {
                    console.error("Erro ao conectar MetaMask ou buscar saldo:", error);
                }
            }
        };
        fetchData();
    }, [userType]);

    const handleCompensate = async () => {
        alert(`Solicitação de compensação para ${userData.name} (${userType}) enviada! (Simulado)`);
        // In a real application, this would interact with your backend API to initiate a compensation transaction.
    };

    return (
        <div className="dashboard-container">
            <h1 className="text-3xl font-bold mb-6">Dashboard - {userData.name} ({userType.charAt(0).toUpperCase() + userType.slice(1)})</h1>
            <p className="text-lg mb-4">Conta Conectada: {connectedAccount || 'Não conectado'}</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="card">
                    <h2 className="card-title">Saldo de Tokens ENRC</h2>
                    <p className="card-value">{balance} ENRC</p>
                </div>
                {userType === 'usina' && (
                    <div className="card">
                        <h2 className="card-title">Geração Simulada (kWh)</h2>
                        <p className="card-value">{userData.generation}</p>
                    </div>
                )}
                {(userType === 'distribuidora' || userType === 'consumidor') && (
                    <div className="card">
                        <h2 className="card-title">Consumo Simulado (kWh)</h2>
                        <p className="card-value">{userData.consumption}</p>
                    </div>
                )}
                <div className="card">
                    <h2 className="card-title">Status da Rede</h2>
                    <p className="card-value text-green-500">Online</p>
                </div>
            </div>

            <div className="action-section">
                <h2 className="text-2xl font-semibold mb-4">Ações</h2>
                <button onClick={handleCompensate} className="btn-primary">
                    Solicitar Compensação de Energia
                </button>
            </div>

            <div className="recent-activity">
                <h2 className="text-2xl font-semibold mb-4">Atividade Recente (Simulada)</h2>
                <ul>
                    <li>2025-06-10: Recebidos 100 ENRC de Usina Solar X</li>
                    <li>2025-06-09: Consumidos 50 kWh de energia</li>
                    <li>2025-06-08: Enviados 20 ENRC para Distribuidora Y</li>
                </ul>
            </div>
        </div>
    );
};

export default Dashboard;



