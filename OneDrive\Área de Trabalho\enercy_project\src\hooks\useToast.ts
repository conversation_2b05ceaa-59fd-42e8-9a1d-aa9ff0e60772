import { useContext } from 'react';
import { ToastContext } from '@/contexts/ToastContext';

export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    // Fallback para quando não há ToastProvider
    return {
      showToast: (message: string, type: 'success' | 'error' | 'info' = 'info') => {
        console.log(`Toast ${type}: ${message}`);
      }
    };
  }
  return context;
};
