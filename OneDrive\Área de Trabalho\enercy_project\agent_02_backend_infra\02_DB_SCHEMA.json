{
    "Users": {
        "_id": "ObjectId",
        "walletAddress": "String",
        "userType": "String", // "usina", "distribuidora", "consumidor"
        "name": "String",
        "email": "String",
        "createdAt": "Date"
    },
    "Plants": {
        "_id": "ObjectId",
        "ownerId": "ObjectId", // Referência ao User
        "name": "String",
        "location": "String",
        "capacity": "Number",
        "generationData": [
            {
                "date": "Date",
                "amountKWh": "Number"
            }
        ],
        "createdAt": "Date"
    },
    "Transactions": {
        "_id": "ObjectId",
        "fromAddress": "String",
        "toAddress": "String",
        "tokenAmount": "Number",
        "transactionType": "String", // "mint", "burn", "transfer", "compensation"
        "blockchainTxHash": "String",
        "status": "String", // "pending", "completed", "failed"
        "createdAt": "Date"
    }
}

