<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Interface ENRC</title>
</head>
<body>
    <h1>ENRC - Token de Energia</h1>
    <button id="connect">Conectar MetaMask</button>
    <p>Saldo: <span id="balance">0</span> ENRC</p>
    <script src="https://cdn.ethers.io/lib/ethers-5.2.umd.min.js"></script>
    <script>
        // Script mínimo de conexão e leitura de saldo
        document.getElementById('connect').addEventListener('click', async () => {
            if (typeof window.ethereum !== 'undefined') {
                try {
                    await window.ethereum.request({ method: 'eth_requestAccounts' });
                    const provider = new ethers.providers.Web3Provider(window.ethereum);
                    const signer = provider.getSigner();
                    const accounts = await provider.listAccounts();
                    console.log("Conectado com a conta:", accounts[0]);

                    // Substitua pelo endereço do seu contrato ENRC e ABI
                    const tokenAddress = "SEU_ENDERECO_DO_CONTRATO_ENRC"; // <<<<<<< ATUALIZE AQUI
                    const tokenAbi = [
                        "function balanceOf(address account) view returns (uint256)",
                        "function symbol() view returns (string)"
                    ];

                    const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, provider);
                    const balance = await tokenContract.balanceOf(accounts[0]);
                    const symbol = await tokenContract.symbol();

                    document.getElementById('balance').innerText = ethers.utils.formatUnits(balance, 18); // Assumindo 18 decimais

                } catch (error) {
                    console.error("Erro ao conectar ou obter saldo:", error);
                }
            } else {
                alert('MetaMask não detectado. Por favor, instale-o.');
            }
        });
    </script>
</body>
</html>

