# ENERCY Backend API

Esta API RESTful é o coração do sistema ENERCY, gerenciando a comunicação entre o frontend, o blockchain e os dados do usuário.

## Setup

1.  **Pré-requisitos:** Node.js e npm instalados.
2.  **Instalação de Dependências:** Navegue até o diretório `enercy_project/agent_02_backend_infra` e execute:
    ```bash
    npm install express
    ```
3.  **Variáveis de Ambiente:** Crie um arquivo `.env` na raiz do diretório do backend com as seguintes variáveis (exemplo):
    ```
    PORT=3000
    MONGODB_URI=mongodb://localhost:27017/enercy_db
    ETHEREUM_NODE_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
    PRIVATE_KEY=YOUR_WALLET_PRIVATE_KEY_FOR_TRANSACTIONS
    ```

## Rotas (Endpoints)

*   **`POST /register`**
    *   **Descrição:** Registra novos usuários (usinas, consumidores, distribuidoras).
    *   **Corpo da Requisição (JSON):**
        ```json
        {
            "walletAddress": "0x...",
            "userType": "consumidor",
            "name": "Nome do Usuário",
            "email": "<EMAIL>"
        }
        ```
*   **`POST /mint`**
    *   **Descrição:** Solicita a cunhagem (mint) de tokens ENERCY.
    *   **Corpo da Requisição (JSON):**
        ```json
        {
            "toAddress": "0x...",
            "amount": 1000
        }
        ```
*   **`POST /burn`**
    *   **Descrição:** Solicita a queima (burn) de tokens ENERCY.
    *   **Corpo da Requisição (JSON):**
        ```json
        {
            "fromAddress": "0x...",
            "amount": 500
        }
        ```
*   **`POST /transfer`**
    *   **Descrição:** Realiza a transferência de tokens ENERCY entre endereços.
    *   **Corpo da Requisição (JSON):**
        ```json
        {
            "fromAddress": "0x...",
            "toAddress": "0x...",
            "amount": 200
        }
        ```

## Como Rodar

Navegue até o diretório `enercy_project/agent_02_backend_infra` e execute:

```bash
node 02_API_BACKEND.js
```

Ou, para desenvolvimento com `nodemon` (instale com `npm install -g nodemon`):

```bash
nodemon 02_API_BACKEND.js
```


