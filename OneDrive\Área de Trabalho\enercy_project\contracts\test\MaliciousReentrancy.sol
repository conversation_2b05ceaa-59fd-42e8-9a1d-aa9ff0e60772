// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "../ENRC.sol";

/**
 * @title MaliciousReentrancy
 * @dev Contract designed to test reentrancy protection in ENRC token
 * @notice This contract is for testing purposes only - demonstrates attack vectors
 */
contract MaliciousReentrancy {
    ENRC public immutable enrcToken;
    bool private attacking = false;
    uint256 private attackCount = 0;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Attempts to perform reentrancy attack on mint function
     */
    function attemptReentrancy() external {
        require(!attacking, "Already attacking");
        attacking = true;
        attackCount = 0;
        
        // Initial mint call that will trigger the attack
        enrcToken.mint(address(this), 1000 * 10**18, "Initial mint");
    }
    
    /**
     * @dev This function will be called when tokens are received
     * It attempts to reenter the mint function
     */
    function onTokenReceived() external {
        if (attacking && attackCount < 3) {
            attackCount++;
            // Attempt to reenter mint function
            enrcToken.mint(address(this), 1000 * 10**18, "Reentrancy attack");
        }
    }
    
    /**
     * @dev Reset attack state
     */
    function resetAttack() external {
        attacking = false;
        attackCount = 0;
    }
    
    /**
     * @dev Get attack status
     */
    function getAttackStatus() external view returns (bool isAttacking, uint256 count) {
        return (attacking, attackCount);
    }
}

/**
 * @title FlashLoanAttacker
 * @dev Contract to test flash loan attack scenarios
 */
contract FlashLoanAttacker {
    ENRC public immutable enrcToken;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Simulates a flash loan attack on energy credits
     */
    function attemptFlashLoanAttack(address victim, uint256 amount) external {
        // Simulate borrowing large amount
        uint256 initialBalance = enrcToken.balanceOf(address(this));
        
        // Transfer from victim (simulating flash loan)
        enrcToken.transferFrom(victim, address(this), amount);
        
        // Attempt to manipulate energy credits
        // This should fail due to verification requirements
        try enrcToken.issueEnergyCredits(address(this), amount, "malicious") {
            // If successful, try to profit
            enrcToken.transfer(victim, amount);
        } catch {
            // Attack failed, return funds
            enrcToken.transfer(victim, amount);
        }
    }
}

/**
 * @title GasGriefingAttacker
 * @dev Contract to test gas griefing attacks
 */
contract GasGriefingAttacker {
    ENRC public immutable enrcToken;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Attempts to consume excessive gas in callbacks
     */
    function attemptGasGriefing() external {
        // This would normally be called in a callback
        // Infinite loop to consume gas
        for (uint256 i = 0; i < 1000000; i++) {
            // Waste gas
            keccak256(abi.encodePacked(i));
        }
    }
    
    /**
     * @dev Fallback function that consumes gas
     */
    fallback() external payable {
        // Consume gas in fallback
        for (uint256 i = 0; i < 1000; i++) {
            keccak256(abi.encodePacked(i, block.timestamp));
        }
    }
}

/**
 * @title FrontRunningBot
 * @dev Contract to simulate front-running attacks
 */
contract FrontRunningBot {
    ENRC public immutable enrcToken;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Attempts to front-run energy credit transactions
     */
    function frontRunTransaction(
        address target,
        uint256 amount,
        string memory energyType
    ) external {
        // Monitor mempool and try to front-run with higher gas
        // This is a simplified simulation
        
        // Attempt to issue credits before the original transaction
        try enrcToken.issueEnergyCredits(address(this), amount, energyType) {
            // Front-run successful
        } catch {
            // Front-run failed
        }
    }
}

/**
 * @title OverflowAttacker
 * @dev Contract to test integer overflow/underflow protection
 */
contract OverflowAttacker {
    ENRC public immutable enrcToken;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Attempts to cause integer overflow
     */
    function attemptOverflow() external {
        // Try to mint maximum uint256 amount
        try enrcToken.mint(address(this), type(uint256).max, "Overflow attack") {
            // Should fail due to max supply check
        } catch {
            // Expected to fail
        }
    }
    
    /**
     * @dev Attempts to cause underflow in energy credits
     */
    function attemptUnderflow() external {
        // Try to redeem more credits than available
        try enrcToken.redeemEnergyCredits(type(uint256).max) {
            // Should fail
        } catch {
            // Expected to fail
        }
    }
}

/**
 * @title AccessControlAttacker
 * @dev Contract to test access control bypasses
 */
contract AccessControlAttacker {
    ENRC public immutable enrcToken;
    
    constructor(address _enrcToken) {
        enrcToken = ENRC(_enrcToken);
    }
    
    /**
     * @dev Attempts to bypass access control
     */
    function attemptPrivilegeEscalation() external {
        bytes32 adminRole = enrcToken.DEFAULT_ADMIN_ROLE();
        bytes32 minterRole = enrcToken.MINTER_ROLE();
        
        // Try to grant roles without permission
        try enrcToken.grantRole(adminRole, address(this)) {
            // Should fail
        } catch {
            // Expected
        }
        
        try enrcToken.grantRole(minterRole, address(this)) {
            // Should fail
        } catch {
            // Expected
        }
    }
    
    /**
     * @dev Attempts to call admin functions without permission
     */
    function attemptUnauthorizedActions() external {
        // Try to pause without permission
        try enrcToken.pause() {
            // Should fail
        } catch {
            // Expected
        }
        
        // Try to add energy provider without permission
        try enrcToken.addEnergyProvider(address(this)) {
            // Should fail
        } catch {
            // Expected
        }
        
        // Try to mint without permission
        try enrcToken.mint(address(this), 1000 * 10**18, "Unauthorized mint") {
            // Should fail
        } catch {
            // Expected
        }
    }
}
