Gere um frontend para o projeto ENERCY usando React e Web3. O frontend deve incluir:
- Um componente de dashboard (`03_UI_DASHBOARD.jsx`) com conexão `ethers.js`.
- Estilos Tailwind CSS (`03_UI_STYLE.css`) para um layout clean e futurista, focado em usabilidade e clareza.
- Suporte a 3 perfis de usuário: usina, distribuidora e consumidor, com dashboards adaptados.
- Funcionalidades como exibição de saldos de tokens ENRC, consumo/geração simulados, e um botão para solicitar compensação de energia.
- Integração com a API de backend e a blockchain para transações e dados.

O protótipo visual deve ser funcional e demonstrar a viabilidade da plataforma.

