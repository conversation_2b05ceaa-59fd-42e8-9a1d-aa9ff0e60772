'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useWeb3 } from './Web3Context';
import { useToast } from '@/hooks/useToast';

export interface User {
  id: string;
  walletAddress: string;
  userType: 'consumidor' | 'usina' | 'distribuidora';
  name: string;
  email: string;
  isVerified: boolean;
  createdAt: string;
  profile?: {
    avatar?: string;
    location?: string;
    energyCapacity?: number;
    monthlyConsumption?: number;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (walletAddress: string) => Promise<void>;
  register: (userData: Partial<User>) => Promise<void>;
  logout: () => void;
  updateProfile: (profileData: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001';

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { account, isConnected } = useWeb3();
  const { showToast } = useToast();

  // Check authentication status on mount and when wallet connects
  useEffect(() => {
    checkAuthStatus();
  }, [account, isConnected]);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    
    try {
      const token = localStorage.getItem('authToken');
      
      if (!token || !account) {
        setUser(null);
        setIsAuthenticated(false);
        setIsLoading(false);
        return;
      }

      // Verify token and get user data
      const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        
        // Check if wallet address matches
        if (userData.walletAddress.toLowerCase() === account.toLowerCase()) {
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          // Wallet address mismatch, logout
          logout();
        }
      } else {
        // Invalid token
        logout();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (walletAddress: string) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ walletAddress }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('authToken', data.token);
        setUser(data.user);
        setIsAuthenticated(true);
        showToast('Login realizado com sucesso!', 'success');
      } else {
        throw new Error(data.message || 'Erro no login');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      showToast(error.message || 'Erro ao fazer login', 'error');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: Partial<User>) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...userData,
          walletAddress: account,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('authToken', data.token);
        setUser(data.user);
        setIsAuthenticated(true);
        showToast('Conta criada com sucesso!', 'success');
      } else {
        throw new Error(data.message || 'Erro no registro');
      }
    } catch (error: any) {
      console.error('Register error:', error);
      showToast(error.message || 'Erro ao criar conta', 'error');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    setUser(null);
    setIsAuthenticated(false);
    showToast('Logout realizado com sucesso', 'info');
  };

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const token = localStorage.getItem('authToken');
      
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      const data = await response.json();

      if (response.ok) {
        setUser(data.user);
        showToast('Perfil atualizado com sucesso!', 'success');
      } else {
        throw new Error(data.message || 'Erro ao atualizar perfil');
      }
    } catch (error: any) {
      console.error('Update profile error:', error);
      showToast(error.message || 'Erro ao atualizar perfil', 'error');
      throw error;
    }
  };

  const refreshUser = async () => {
    try {
      const token = localStorage.getItem('authToken');
      
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
