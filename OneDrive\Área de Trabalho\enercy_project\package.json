{"name": "enercy-mvp", "version": "1.0.0", "description": "ENERCY - Plataforma de Tokenização de Energia Descentralizada", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "backend:dev": "nodemon backend/server.js", "backend:start": "node backend/server.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:testnet": "hardhat run scripts/deploy.js --network sepolia", "compile:contracts": "hardhat compile", "test:contracts": "hardhat test"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/react": "^18.2.0", "@types/node": "^20.0.0", "tailwindcss": "^3.3.0", "ethers": "^6.8.0", "web3": "^4.2.0", "express": "^4.18.0", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "mongoose": "^8.0.0", "dotenv": "^16.3.0", "winston": "^3.11.0", "joi": "^17.11.0", "@openzeppelin/contracts": "^5.0.0", "hardhat": "^2.19.0", "@nomicfoundation/hardhat-toolbox": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "prettier": "^3.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "keywords": ["blockchain", "energy", "tokenization", "renewable", "defi", "web3"], "author": "ENERCY Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/enercy/enercy-mvp.git"}}