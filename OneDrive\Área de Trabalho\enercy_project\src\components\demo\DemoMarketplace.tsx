'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';

interface DemoMarketplaceProps {
  offers: any[];
  userType: string;
}

export default function DemoMarketplace({ offers, userType }: DemoMarketplaceProps) {
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [filter, setFilter] = useState('all');

  const getEnergyTypeIcon = (type: string) => {
    switch (type) {
      case 'solar': return '☀️';
      case 'wind': return '💨';
      case 'hydro': return '💧';
      case 'biomass': return '🌱';
      default: return '⚡';
    }
  };

  const getEnergyTypeColor = (type: string) => {
    switch (type) {
      case 'solar': return 'from-yellow-400 to-orange-500';
      case 'wind': return 'from-blue-400 to-blue-600';
      case 'hydro': return 'from-cyan-400 to-blue-500';
      case 'biomass': return 'from-green-400 to-green-600';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const filteredOffers = filter === 'all' ? offers : offers.filter(offer => offer.energyType === filter);

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Marketplace de Energia Renovável
          </h1>
          <p className="text-gray-400">
            Conectando produtores e consumidores diretamente
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-white/5 rounded-lg px-4 py-2 border border-white/10">
            <div className="text-sm text-gray-400">Volume Total</div>
            <div className="text-lg font-bold text-green-400">
              {offers.reduce((sum, offer) => sum + offer.amount, 0).toLocaleString()} kWh
            </div>
          </div>
          <div className="bg-white/5 rounded-lg px-4 py-2 border border-white/10">
            <div className="text-sm text-gray-400">Preço Médio</div>
            <div className="text-lg font-bold text-blue-400">
              R$ {(offers.reduce((sum, offer) => sum + offer.pricePerKwh, 0) / offers.length).toFixed(3)}/kWh
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <span className="text-gray-400">Filtrar por tipo:</span>
        <div className="flex space-x-2">
          {['all', 'solar', 'wind', 'hydro', 'biomass'].map((type) => (
            <button
              key={type}
              onClick={() => setFilter(type)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                filter === type
                  ? 'bg-gradient-to-r from-green-500 to-blue-600 text-white'
                  : 'bg-white/5 text-gray-400 hover:text-white hover:bg-white/10'
              }`}
            >
              {type === 'all' ? '🔍 Todos' : `${getEnergyTypeIcon(type)} ${type.charAt(0).toUpperCase() + type.slice(1)}`}
            </button>
          ))}
        </div>
      </div>

      {/* Offers Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredOffers.map((offer, index) => (
          <motion.div
            key={offer.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer"
            onClick={() => setSelectedOffer(offer)}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${getEnergyTypeColor(offer.energyType)} text-white text-lg`}>
                  {getEnergyTypeIcon(offer.energyType)}
                </div>
                <div>
                  <h3 className="font-semibold text-white">{offer.seller.name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-400 capitalize">{offer.energyType}</span>
                    {offer.seller.verified && (
                      <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(offer.seller.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-600'
                      }`}
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                  <span className="text-sm text-gray-400 ml-1">{offer.seller.rating}</span>
                </div>
              </div>
            </div>

            {/* Main Info */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-2xl font-bold text-white">
                  {offer.amount.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">kWh disponíveis</div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-400">
                  R$ {offer.pricePerKwh.toFixed(3)}
                </div>
                <div className="text-sm text-gray-400">por kWh</div>
              </div>
            </div>

            {/* Location and Efficiency */}
            <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>{offer.location}</span>
              </div>
              <div className="text-green-400">
                {offer.efficiency}% eficiência
              </div>
            </div>

            {/* Certificates */}
            <div className="flex flex-wrap gap-2 mb-4">
              {offer.certificates.slice(0, 2).map((cert: string) => (
                <span
                  key={cert}
                  className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30"
                >
                  {cert}
                </span>
              ))}
              {offer.certificates.length > 2 && (
                <span className="px-2 py-1 bg-gray-500/20 text-gray-300 text-xs rounded-full">
                  +{offer.certificates.length - 2}
                </span>
              )}
            </div>

            {/* Action Button */}
            <div className="flex items-center justify-between">
              <div className="text-right">
                <div className="text-lg font-bold text-white">
                  R$ {(offer.amount * offer.pricePerKwh).toLocaleString()}
                </div>
                <div className="text-xs text-gray-400">Total</div>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                  userType === 'usina'
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-green-500 to-blue-600 text-white hover:from-green-600 hover:to-blue-700'
                }`}
                disabled={userType === 'usina'}
              >
                {userType === 'usina' ? 'Não disponível' : 'Comprar'}
              </motion.button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Market Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
      >
        <h3 className="text-xl font-semibold text-white mb-6">Estatísticas do Mercado</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">
              {offers.length}
            </div>
            <div className="text-gray-400">Ofertas Ativas</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">
              {Math.round(offers.filter(o => o.seller.verified).length / offers.length * 100)}%
            </div>
            <div className="text-gray-400">Vendedores Verificados</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">
              {offers.reduce((sum, offer) => sum + offer.co2Avoided * offer.amount / 1000, 0).toFixed(1)}
            </div>
            <div className="text-gray-400">ton CO₂ Evitadas</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">
              &lt;30s
            </div>
            <div className="text-gray-400">Tempo Médio de Transação</div>
          </div>
        </div>
      </motion.div>

      {/* Live Activity Feed */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
      >
        <h3 className="text-xl font-semibold text-white mb-6">Atividade em Tempo Real</h3>
        
        <div className="space-y-3">
          {[
            { time: '14:32', action: 'Nova oferta', details: 'Fazenda Solar Nordeste - 5 MWh', type: 'new' },
            { time: '14:28', action: 'Transação concluída', details: 'Indústria ABC comprou 2 MWh', type: 'success' },
            { time: '14:25', action: 'Preço atualizado', details: 'Energia eólica: R$ 0.095/kWh', type: 'update' },
            { time: '14:22', action: 'Certificado emitido', details: 'I-REC para 3 MWh solares', type: 'certificate' }
          ].map((activity, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 + index * 0.1 }}
              className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'success' ? 'bg-green-400' :
                  activity.type === 'new' ? 'bg-blue-400' :
                  activity.type === 'certificate' ? 'bg-purple-400' :
                  'bg-yellow-400'
                }`} />
                <div>
                  <div className="text-white font-medium">{activity.action}</div>
                  <div className="text-gray-400 text-sm">{activity.details}</div>
                </div>
              </div>
              <div className="text-gray-400 text-sm">{activity.time}</div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
