# ENERCY Frontend UI

Este é o protótipo da interface de usuário (UI) para o projeto ENERCY, desenvolvido com React e Tailwind CSS.

## Setup

1.  **Pré-requisitos:** Node.js e npm (ou yarn) instalados.
2.  **Instalação de Dependências:** Navegue até o diretório `enercy_project/agent_03_frontend_ui`.
    Se este for um projeto React completo, você precisará inicializá-lo primeiro (ex: `npx create-react-app .` ou `yarn create react-app .`) e depois instalar as dependências.
    Para este MVP, assumimos que `03_UI_DASHBOARD.jsx` e `03_UI_STYLE.css` serão integrados a um projeto React existente ou a um ambiente de desenvolvimento.
    Instale as dependências necessárias:
    ```bash
    npm install react react-dom ethers
    npm install -D tailwindcss postcss autoprefixer
    npx tailwindcss init -p
    ```
    Configure o `tailwind.config.js` para incluir os arquivos React.

3.  **Configurar RPC e Contrato:**
    No arquivo `03_UI_DASHBOARD.jsx`, atualize `SEU_ENDERECO_DO_CONTRATO_ENRC` com o endereço do contrato do token ENRC implantado na testnet.

## Como Rodar

Para rodar o frontend, você precisará integrá-lo a um projeto React completo e usar o comando de inicialização do React:

```bash
npm start
# ou
yarn start
```

Certifique-se de que o backend esteja rodando e que o MetaMask esteja configurado e conectado à testnet correta.

## Funcionalidades (MVP)

*   Conexão com MetaMask.
*   Exibição do saldo de tokens ENRC.
*   Exibição de dados simulados de geração/consumo para diferentes tipos de usuário (usina, distribuidora, consumidor).
*   Botão para simular solicitação de compensação de energia.
*   Estilo clean e responsivo com Tailwind CSS.


