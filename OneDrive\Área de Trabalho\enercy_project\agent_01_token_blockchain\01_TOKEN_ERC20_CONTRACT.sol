// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title ENRC Token
 * @dev Implementation of the ENERCY Credit token with enhanced security features
 */
contract ENRC is ERC20, ERC20Burnable, Pausable, Ownable, ReentrancyGuard {
    // Events for better transparency and monitoring
    event TokensMinted(address indexed to, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
    
    // Role-based access control could be implemented for more granular permissions
    
    constructor() ERC20("ENERCY Credit", "ENRC") {
        _mint(msg.sender, 10_000_000 * 10 ** decimals());
    }
    
    function pause() public onlyOwner {
        _pause();
    }
    
    function unpause() public onlyOwner {
        _unpause();
    }
    
    function mint(address to, uint256 amount) public onlyOwner nonReentrant {
        require(to != address(0), "Cannot mint to zero address");
        _mint(to, amount);
        emit TokensMinted(to, amount);
    }
    
    function burn(uint256 amount) public override nonReentrant {
        super.burn(amount);
        emit TokensBurned(_msgSender(), amount);
    }
    
    function _beforeTokenTransfer(address from, address to, uint256 amount)
        internal
        whenNotPaused
        override
    {
        super._beforeTokenTransfer(from, to, amount);
    }
}


