// 02_API_BACKEND.js
const express = require("express");
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

// Endpoints
app.post("/register", (req, res) => {
    // Lógica de registro de usinas, consumidores e distribuidoras
    console.log("Registro solicitado:", req.body);
    res.status(200).json({ message: "Registro recebido com sucesso!" });
});

app.post("/mint", (req, res) => {
    // Lógica para mint de tokens ENERCY
    console.log("Mint solicitado:", req.body);
    res.status(200).json({ message: "Solicitação de mint recebida!" });
});

app.post("/burn", (req, res) => {
    // Lógica para burn de tokens ENERCY
    console.log("Burn solicitado:", req.body);
    res.status(200).json({ message: "Solicitação de burn recebida!" });
});

app.post("/transfer", (req, res) => {
    // Lógica para transferência de tokens ENERCY
    console.log("Transferência solicitada:", req.body);
    res.status(200).json({ message: "Solicitação de transferência recebida!" });
});

app.listen(port, () => {
    console.log(`Backend API rodando em http://localhost:${port}`);
});


