@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    font-family: Arial, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    line-height: 1.6;
}

.dashboard-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #2c3e50;
    border-bottom: 2px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 25px;
}

h2 {
    color: #34495e;
    margin-bottom: 15px;
}

.card {
    background-color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.card-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.card-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2980b9;
}

.btn-primary {
    background-color: #3498db;
    color: white;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.action-section, .recent-activity {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.recent-activity ul {
    list-style: none;
    padding: 0;
}

.recent-activity li {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 4px;
}


