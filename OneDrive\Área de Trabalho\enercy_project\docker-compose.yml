version: '3.8'

services:
  # Frontend Next.js Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_BACKEND_URL=http://backend:3001
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
      - mongodb
    networks:
      - enercy-network

  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/enercy_db
      - JWT_SECRET=your_jwt_secret_here
      - PORT=3001
    volumes:
      - ./backend:/app/backend
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - enercy-network

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - M<PERSON><PERSON><PERSON>_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=enercy_db
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - enercy-network

  # Redis for Caching and Sessions
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - enercy-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - enercy-network

  # Blockchain Development Node (Ganache)
  ganache:
    image: trufflesuite/ganache:latest
    ports:
      - "8545:8545"
    command: >
      ganache
      --host 0.0.0.0
      --port 8545
      --networkId 1337
      --accounts 10
      --deterministic
      --mnemonic "your development mnemonic here"
    networks:
      - enercy-network

volumes:
  mongodb_data:
  redis_data:

networks:
  enercy-network:
    driver: bridge
