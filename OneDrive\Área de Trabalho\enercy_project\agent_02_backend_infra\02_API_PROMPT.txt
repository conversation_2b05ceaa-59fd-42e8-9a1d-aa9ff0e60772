Gere uma API RESTful em Node.js/Express (ou Python FastAPI) para o projeto ENERCY. A API deve incluir endpoints para:
- Registro de usuários (usinas, consumidores, distribuidoras).
- Operações de mint, burn e transferências de tokens ENERCY.
- Conexão com carteira Web3 padrão (MetaMask) para assinatura de transações.
- Simulação de consumo de energia e armazenamento de dados (MongoDB ou Firebase).

Considere a estrutura de dados definida no 02_DB_SCHEMA.json. Inclua exemplos de requisições e respostas para cada endpoint.

